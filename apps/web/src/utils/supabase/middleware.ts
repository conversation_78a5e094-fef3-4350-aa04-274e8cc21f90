import { createServerClient } from '@supabase/ssr';
import { NextResponse, type NextRequest } from 'next/server';

export async function updateSession(request: NextRequest) {
  console.log("🔍 [MIDDLEWARE] updateSession called for:", request.url);

  let supabaseResponse = NextResponse.next({
    request,
  });

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          const cookies = request.cookies.getAll();
          console.log("🔍 [MIDDLEWARE] Getting cookies:", cookies.filter(c => c.name.includes('supabase') || c.name.includes('sb-')).map(c => ({ name: c.name, hasValue: !!c.value })));
          return cookies;
        },
        setAll(cookiesToSet) {
          console.log("🔍 [MIDDLEWARE] Setting cookies:", cookiesToSet.map(c => ({ name: c.name, hasValue: !!c.value })));
          cookiesToSet.forEach(({ name, value, options }) => {
            request.cookies.set({ name, value, ...options });  // Use object form with options
          });
          supabaseResponse = NextResponse.next({
            request,
          });
          cookiesToSet.forEach(({ name, value, options }) => {
            supabaseResponse.cookies.set({ name, value, ...options });  // Preserve Supabase options, no overrides
          });
        },
      },
    }
  );

  // IMPORTANT: Avoid writing any logic between createServerClient and
  // supabase.auth.getUser(). A simple mistake could make it very hard to debug
  // issues with users being randomly logged out.

  const {
    data: { user },
    error: userError
  } = await supabase.auth.getUser();

  console.log("🔍 [MIDDLEWARE] User from getUser():", {
    hasUser: !!user,
    userEmail: user?.email,
    userId: user?.id,
    userError: userError?.message,
    userLastSignIn: user?.last_sign_in_at
  });

  console.log("🔍 [MIDDLEWARE] ==================== END MIDDLEWARE ====================");

  // IMPORTANT: You *must* return the supabaseResponse object as it is.
  // If you're creating a new response object with NextResponse.next() make sure to:
  // 1. Pass the request in it, like so:
  //    const myNewResponse = NextResponse.next({ request })
  // 2. Copy over the cookies, like so:
  //    myNewResponse.cookies.setAll(supabaseResponse.cookies.getAll())
  // 3. Change the myNewResponse object to fit your needs, but avoid changing
  //    the cookies!
  // 4. Finally:
  //    return myNewResponse
  // If this is not done, you may be causing the browser and server to go out
  // of sync and terminate the user's session prematurely!

  return supabaseResponse;
}